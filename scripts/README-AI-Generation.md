# AI Style Sample Generation

This document explains how to generate actual AI images for each image style using your dreamstarter-agent system.

## Overview

The `generate-ai-style-samples.js` script creates real AI-generated sample images for each style in your ImageStyles array. These images showcase what each style actually looks like when applied to image generation.

## Prerequisites

### 1. Running Agent Server
Your dreamstarter-agent server must be running and accessible:

```bash
# Start your agent server (typically on port 2151)
npm start
# or
node dist/index.js
```

### 2. Environment Variables
Set the following environment variables:

```bash
# Required: URL where your agent server is running
export AGENT_URL="http://localhost:2151"

# Required: Agent ID to use for generation
export AGENT_ID="your-agent-id"

# Optional: Override default settings
export DELAY_BETWEEN_REQUESTS=3000  # milliseconds
export MAX_RETRIES=2
```

### 3. Image Generation Provider
Ensure your agent has at least one image generation provider configured:

- **ANTHROPIC_API_KEY** - For Anthropic image generation
- **TOGETHER_API_KEY** - For Together AI (FLUX models)
- **HEURIST_API_KEY** - For Heurist
- **FAL_API_KEY** - For FAL
- **OPENAI_API_KEY** - For OpenAI DALL-E
- **VENICE_API_KEY** - For Venice AI
- **LIVEPEER_GATEWAY_URL** - For Livepeer

## Usage

### Basic Generation
```bash
npm run generate-ai-style-samples
```

### With Custom Settings
```bash
AGENT_URL="http://your-server:2151" AGENT_ID="your-agent" npm run generate-ai-style-samples
```

## How It Works

### 1. Style-Specific Prompts
The script uses carefully crafted prompts for each style to showcase their unique characteristics:

- **Watercolor**: "A dreamy mountain landscape with soft flowing colors and wet paint effects"
- **Cyberpunk**: "A futuristic mountain landscape with neon lights and tech elements"
- **Pencil Sketch**: "A detailed pencil drawing of a mountain landscape with fine lines"
- **And 60+ more styles...**

### 2. API Integration
The script calls your `/:agentId/post-image-gen` endpoint with:
```json
{
  "description": "Style-specific prompt",
  "planId": "style-samples",
  "style": "style_name"
}
```

### 3. Image Processing
- Downloads generated images from URLs or copies local files
- Saves images as `{style_name}.jpg` in `public/images/style-samples/`
- Creates metadata file with generation details

### 4. Error Handling
- Automatic retries for failed generations
- Rate limiting between requests
- Detailed logging of success/failure
- Graceful handling of API errors

## Output

### Generated Files
```
public/images/style-samples/
├── none.jpg                    # No style sample
├── watercolor.jpg             # Watercolor style sample
├── cyberpunk.jpg              # Cyberpunk style sample
├── pencil_sketch.jpg          # Pencil sketch sample
├── ...                        # All other styles
└── ai-generation-metadata.json # Generation details
```

### Metadata File
The metadata file contains:
```json
{
  "generated_at": "2024-01-15T10:30:00.000Z",
  "agent_url": "http://localhost:2151",
  "agent_id": "sample-agent",
  "base_prompt": "A beautiful mountain landscape with a serene lake",
  "uses_style_specific_prompts": true,
  "total_styles": 67,
  "successful": 65,
  "newly_generated": 65,
  "skipped": 0,
  "failed": 2,
  "results": [...]
}
```

## Integration with Frontend

### Automatic Usage
The frontend canvas editor automatically uses these AI-generated images:

1. **Priority Order**: AI images → SVG placeholders → CSS gradients
2. **File Detection**: Checks for `.jpg` files first, falls back to `.svg`
3. **Caching**: Images are cached by the browser for performance

### Manual Integration
If you need to manually reference the images:

```typescript
import { getSampleImageUrl } from '@/common/utils/sampleImages';

const imageUrl = getSampleImageUrl('watercolor'); // Returns URL to sample image
```

## Troubleshooting

### Common Issues

#### 1. "Agent not found" Error
```bash
# Check if agent server is running
curl http://localhost:2151/health

# Verify agent ID exists
curl http://localhost:2151/agents
```

#### 2. "No image path found in API response"
- Check that your agent has image generation providers configured
- Verify API keys are set correctly
- Check agent logs for detailed error messages

#### 3. Generation Fails for Specific Styles
- Some styles may not be supported by all image providers
- Check the metadata file to see which styles failed
- Consider using different image generation providers

#### 4. Rate Limiting Issues
```bash
# Increase delay between requests
DELAY_BETWEEN_REQUESTS=5000 npm run generate-ai-style-samples
```

### Debug Mode
Enable verbose logging:
```bash
DEBUG=true npm run generate-ai-style-samples
```

## Performance Considerations

### Generation Time
- **Total time**: ~5-10 minutes for all 67 styles
- **Per image**: ~3-5 seconds generation + 3 seconds delay
- **Parallel processing**: Not recommended (may hit rate limits)

### Resource Usage
- **Disk space**: ~50-200KB per image (3-13MB total)
- **Network**: Depends on image generation provider
- **Memory**: Minimal (streaming downloads)

## Customization

### Adding New Styles
1. Add style to `ImageStyles` array in `common/constants.ts`
2. Add style-specific prompt in `getStyleSpecificPrompt()` function
3. Re-run the generation script

### Changing Base Prompt
Edit the `BASE_PROMPT` constant in the script:
```javascript
const BASE_PROMPT = "Your custom base description";
```

### Custom Output Directory
Modify the `OUTPUT_DIR` constant:
```javascript
const OUTPUT_DIR = path.join(__dirname, '..', 'your', 'custom', 'path');
```

## Production Deployment

### CI/CD Integration
```yaml
# Example GitHub Actions workflow
- name: Generate Style Samples
  run: |
    export AGENT_URL="${{ secrets.AGENT_URL }}"
    export AGENT_ID="${{ secrets.AGENT_ID }}"
    npm run generate-ai-style-samples
```

### CDN Deployment
After generation, upload images to your CDN:
```bash
# Example with AWS S3
aws s3 sync public/images/style-samples/ s3://your-bucket/style-samples/
```

## Monitoring

### Success Metrics
- Check metadata file for success/failure counts
- Monitor generation time trends
- Track image file sizes

### Alerts
Set up monitoring for:
- Generation failures > 10%
- Total generation time > 15 minutes
- Missing image files in production

## Next Steps

1. **Run the script**: `npm run generate-ai-style-samples`
2. **Check results**: Review generated images and metadata
3. **Test frontend**: Verify images appear in canvas editor
4. **Optimize**: Adjust prompts or settings as needed
5. **Automate**: Set up regular regeneration if styles change

For questions or issues, check the agent server logs and the generated metadata file for detailed information.
