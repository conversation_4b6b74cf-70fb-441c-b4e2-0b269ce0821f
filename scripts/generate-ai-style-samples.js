#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to generate actual AI images for each image style using the post-image-gen endpoint
 * This creates real AI-generated sample images for the canvas editor
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// Complete ImageStyles from your constants.ts - exactly matching your system
const ImageStyles = [
  { option: "none", label: "No Style" },
  // Medium styles
  { option: "stencil", label: "Stencil" },
  { option: "watercolor", label: "Watercolor" },
  { option: "papercraft", label: "Papercraft" },
  { option: "marker_illustration", label: "Marker Illustration" },
  { option: "risograph", label: "Risograph" },
  { option: "graffiti", label: "Graffiti" },
  { option: "ink_wash", label: "Ink Wash" },
  { option: "quilling", label: "Quilling" },
  { option: "charcoal", label: "Charcoal" },
  { option: "oil_painting", label: "Oil Painting" },
  { option: "collage", label: "Collage" },
  // Photography styles
  { option: "high_key_photograph", label: "High Key Photo" },
  { option: "low_key_photograph", label: "Low Key Photo" },
  { option: "low_angle_photograph", label: "Low Angle Photo" },
  { option: "high_angle_photograph", label: "High Angle Photo" },
  { option: "extreme_close_up", label: "Extreme Close Up" },
  { option: "low_shutter_speed_photograph", label: "Low Shutter Speed" },
  { option: "bokeh_photograph", label: "Bokeh Photo" },
  { option: "silhouette_photograph", label: "Silhouette Photo" },
  { option: "studio_lighting", label: "Studio Lighting" },
  { option: "black_and_white_photograph", label: "B&W Photo" },
  { option: "birds_eye_view", label: "Bird's Eye View" },
  { option: "worms_eye_view", label: "Worm's Eye View" },
  { option: "dutch_angle", label: "Dutch Angle" },
  { option: "long_exposure_photograph", label: "Long Exposure" },
  // Lighting styles
  { option: "natural_lighting", label: "Natural Lighting" },
  { option: "light_and_shadow", label: "Light and Shadow" },
  { option: "volumetric_lighting", label: "Volumetric Lighting" },
  { option: "neon_lighting", label: "Neon Lighting" },
  { option: "golden_hour", label: "Golden Hour" },
  { option: "blue_hour", label: "Blue Hour" },
  { option: "backlighting", label: "Backlighting" },
  { option: "chiaroscuro", label: "Chiaroscuro" },
  { option: "god_rays", label: "God Rays" },
  { option: "candlelight", label: "Candlelight" },
  { option: "street_lighting", label: "Street Lighting" },
  { option: "softbox_lighting", label: "Softbox Lighting" },
  { option: "moonlight", label: "Moonlight" },
  { option: "fairy_lights", label: "Fairy Lights" },
  // Color and palette styles
  { option: "cool_tones", label: "Cool Tones" },
  { option: "warm_tones", label: "Warm Tones" },
  { option: "pastels", label: "Pastels" },
  { option: "vibrant", label: "Vibrant" },
  { option: "earth_tones", label: "Earth Tones" },
  { option: "jewel_tones", label: "Jewel Tones" },
  { option: "monochromatic_blues", label: "Monochromatic Blues" },
  { option: "earthy_reds_and_oranges", label: "Earthy Reds & Oranges" },
  { option: "neon_graffiti", label: "Neon Graffiti" },
  { option: "autumn_leaves", label: "Autumn Leaves" },
  { option: "deep_sea_blues", label: "Deep Sea Blues" },
  { option: "grayscale", label: "Grayscale" },
  { option: "sepia", label: "Sepia" },
  { option: "primary_colors", label: "Primary Colors" },
  { option: "rainbow_spectrum", label: "Rainbow Spectrum" },
  { option: "metallics", label: "Metallics" },
  // Original styles to maintain compatibility
  { option: "photorealistic", label: "Photorealistic" },
  { option: "pixel_art", label: "Pixel Art" },
  { option: "pencil_sketch", label: "Pencil Sketch" },
  { option: "cyberpunk", label: "Cyberpunk" },
  { option: "impressionist", label: "Impressionist" },
  { option: "abstract", label: "Abstract" },
  { option: "pop_art", label: "Pop Art" },
  { option: "isometric", label: "Isometric" },
  { option: "ukiyo_e", label: "Ukiyo-e" },
  { option: "low_poly", label: "Low Poly" },
];

// Configuration
const OUTPUT_DIR = path.join(__dirname, '..', 'public', 'images', 'style-samples');
const AGENT_URL = process.env.AGENT_URL || 'http://localhost:2151';
const AGENT_ID = process.env.AGENT_ID || 'sample-agent';
const BASE_PROMPT = "A beautiful mountain landscape with a serene lake";
const DELAY_BETWEEN_REQUESTS = 3000; // 3 seconds between requests
const MAX_RETRIES = 2;

/**
 * Get style-specific prompt that best showcases the style
 */
function getStyleSpecificPrompt(styleOption) {
  const stylePrompts = {
    none: BASE_PROMPT,
    // Medium styles
    stencil: "A mountain landscape with clean geometric shapes and bold outlines",
    watercolor: "A dreamy mountain landscape with soft flowing colors and wet paint effects",
    papercraft: "A layered paper mountain scene with dimensional cutout elements",
    marker_illustration: "A vibrant mountain landscape with bold marker strokes and bright colors",
    risograph: "A mountain scene with limited color palette and screen printing texture",
    graffiti: "An urban-style mountain landscape with spray paint effects and bold colors",
    ink_wash: "A traditional mountain landscape with flowing ink and water effects",
    quilling: "A mountain scene made of rolled paper strips and delicate curves",
    charcoal: "A dramatic mountain landscape with deep blacks and soft gray tones",
    oil_painting: "A classical mountain landscape with rich textures and blended colors",
    collage: "A mountain scene assembled from various paper textures and materials",
    // Photography styles
    high_key_photograph: "A bright, airy mountain landscape with soft lighting and minimal shadows",
    low_key_photograph: "A moody mountain landscape with dramatic shadows and dark tones",
    low_angle_photograph: "A mountain landscape shot from below, emphasizing height and grandeur",
    high_angle_photograph: "A mountain landscape viewed from above, showing the valley below",
    extreme_close_up: "An intimate detail of mountain rocks and lake water surface",
    low_shutter_speed_photograph: "A mountain landscape with motion blur in flowing water",
    bokeh_photograph: "A mountain landscape with beautiful background blur and depth",
    silhouette_photograph: "Mountain peaks silhouetted against a dramatic sky",
    studio_lighting: "A mountain landscape with controlled, professional lighting",
    black_and_white_photograph: "A classic monochrome mountain landscape with rich contrast",
    birds_eye_view: "An aerial view of mountains and lake from high above",
    worms_eye_view: "A mountain landscape viewed from ground level looking up",
    dutch_angle: "A tilted perspective of mountain landscape creating dynamic tension",
    long_exposure_photograph: "A mountain landscape with smooth water and cloud streaks",
    // Lighting styles
    natural_lighting: "A mountain landscape bathed in soft natural sunlight",
    light_and_shadow: "A mountain scene with dramatic interplay of light and shadow",
    volumetric_lighting: "A mountain landscape with visible light rays through mist",
    neon_lighting: "A futuristic mountain landscape with electric neon accents",
    golden_hour: "A mountain landscape during golden hour with warm, glowing light",
    blue_hour: "A mountain landscape during blue hour with cool twilight tones",
    backlighting: "A mountain landscape with dramatic backlighting from the sun",
    chiaroscuro: "A mountain landscape with strong contrast between light and dark",
    god_rays: "A mountain landscape with divine light rays breaking through clouds",
    candlelight: "A cozy mountain cabin scene illuminated by warm candlelight",
    street_lighting: "A mountain town landscape with warm street lamp illumination",
    softbox_lighting: "A mountain landscape with soft, even studio-style lighting",
    moonlight: "A mystical mountain landscape bathed in silver moonlight",
    fairy_lights: "A magical mountain landscape decorated with twinkling lights",
    // Color and palette styles
    cool_tones: "A mountain landscape in blues, purples, and cool greens",
    warm_tones: "A mountain landscape in oranges, reds, and warm yellows",
    pastels: "A soft mountain landscape in gentle pastel colors",
    vibrant: "A mountain landscape with intensely saturated, vivid colors",
    earth_tones: "A mountain landscape in natural browns, greens, and earth colors",
    jewel_tones: "A mountain landscape in rich emerald, sapphire, and ruby colors",
    monochromatic_blues: "A mountain landscape rendered entirely in shades of blue",
    earthy_reds_and_oranges: "A mountain landscape in warm terracotta and rust tones",
    neon_graffiti: "A mountain landscape with electric neon and street art colors",
    autumn_leaves: "A mountain landscape in golden autumn foliage colors",
    deep_sea_blues: "A mountain landscape in deep ocean blue tones",
    grayscale: "A mountain landscape in pure black, white, and gray tones",
    sepia: "A vintage mountain landscape in warm brown sepia tones",
    primary_colors: "A mountain landscape using only red, blue, and yellow",
    rainbow_spectrum: "A mountain landscape with full rainbow color spectrum",
    metallics: "A mountain landscape with metallic gold, silver, and copper tones",
    // Original styles
    photorealistic: "A hyper-realistic mountain landscape with perfect detail and clarity",
    pixel_art: "A retro 8-bit style mountain landscape with pixelated graphics",
    pencil_sketch: "A detailed pencil drawing of a mountain landscape with fine lines",
    cyberpunk: "A futuristic mountain landscape with neon lights and tech elements",
    impressionist: "A mountain landscape with loose brushstrokes and light effects",
    abstract: "An abstract interpretation of mountain landscape with geometric forms",
    pop_art: "A mountain landscape in bold pop art style with bright colors",
    isometric: "A mountain landscape in isometric 3D perspective view",
    ukiyo_e: "A mountain landscape in traditional Japanese woodblock print style",
    low_poly: "A mountain landscape with geometric low-polygon 3D style",
  };

  return stylePrompts[styleOption] || BASE_PROMPT;
}

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Download file from URL
 */
function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    const file = fs.createWriteStream(filepath);

    protocol.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: ${response.statusCode}`));
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        resolve();
      });

      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', reject);
  });
}

/**
 * Generate image using the post-image-gen API
 */
async function generateImageForStyle(style, retryCount = 0) {
  const endpoint = `${AGENT_URL}/${AGENT_ID}/post-image-gen`;

  // Use style-specific prompt for better results
  const stylePrompt = getStyleSpecificPrompt(style.option);

  const requestBody = {
    description: stylePrompt,
    planId: 'style-samples',
  };

  // Add style parameter if not 'none'
  if (style.option !== 'none') {
    requestBody.style = style.option;
  }

  try {
    console.log(`🎨 Generating ${style.label} (${style.option})...`);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`📄 API Response for ${style.label}:`, JSON.stringify(data, null, 2));

    // Extract image path from response
    let imagePath = null;
    if (data.filepath) {
      imagePath = data.filepath;
    } else if (data.images && data.images.data && data.images.data.length > 0) {
      imagePath = data.images.data[0];
    } else if (data.image) {
      imagePath = data.image;
    }

    if (!imagePath) {
      throw new Error('No image path found in API response');
    }

    return imagePath;
  } catch (error) {
    console.error(`❌ Failed to generate ${style.label}: ${error.message}`);

    if (retryCount < MAX_RETRIES) {
      console.log(`🔄 Retrying ${style.label} (attempt ${retryCount + 1}/${MAX_RETRIES})...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_REQUESTS));
      return generateImageForStyle(style, retryCount + 1);
    }

    return null;
  }
}

/**
 * Save image to local filesystem
 */
async function saveImageLocally(imagePath, style) {
  try {
    // Determine file extension
    const isUrl = imagePath.startsWith('http');
    const extension = isUrl ? '.jpg' : '.png';
    const filename = `${style.option}${extension}`;
    const localPath = path.join(OUTPUT_DIR, filename);

    if (isUrl) {
      // Download from URL
      await downloadFile(imagePath, localPath);
    } else {
      // Copy from local path (if it's a local file path)
      if (fs.existsSync(imagePath)) {
        fs.copyFileSync(imagePath, localPath);
      } else {
        throw new Error(`Local file not found: ${imagePath}`);
      }
    }

    console.log(`💾 Saved ${style.label} to ${filename}`);
    return filename;
  } catch (error) {
    console.error(`❌ Failed to save ${style.label}: ${error.message}`);
    return null;
  }
}

/**
 * Generate all style samples
 */
async function generateAllStyleSamples() {
  console.log('🚀 Starting AI image generation for style samples...');
  console.log(`📡 Agent URL: ${AGENT_URL}`);
  console.log(`🤖 Agent ID: ${AGENT_ID}`);
  console.log(`📁 Output directory: ${OUTPUT_DIR}`);
  console.log(`📝 Base prompt: "${BASE_PROMPT}"`);
  console.log(`🎨 Using style-specific prompts for better results`);
  console.log(`⏱️  Delay between requests: ${DELAY_BETWEEN_REQUESTS}ms`);
  console.log(`🔄 Max retries per style: ${MAX_RETRIES}`);
  console.log(`📊 Total styles to generate: ${ImageStyles.length}\n`);

  const results = [];

  for (let i = 0; i < ImageStyles.length; i++) {
    const style = ImageStyles[i];
    const filename = `${style.option}.jpg`;
    const filepath = path.join(OUTPUT_DIR, filename);

    // Skip if file already exists
    if (fs.existsSync(filepath)) {
      console.log(`⏭️  ${style.label} (${i + 1}/${ImageStyles.length}) - Already exists`);
      results.push({
        style,
        success: true,
        filepath: filename,
        type: 'existing',
        skipped: true
      });
      continue;
    }

    console.log(`\n🎯 Processing ${style.label} (${i + 1}/${ImageStyles.length})`);

    try {
      // Generate image
      const imagePath = await generateImageForStyle(style);

      if (imagePath) {
        // Save image locally
        const savedFilename = await saveImageLocally(imagePath, style);

        if (savedFilename) {
          console.log(`✅ ${style.label} - Generated successfully`);
          results.push({
            style,
            success: true,
            filepath: savedFilename,
            type: 'generated',
            originalPath: imagePath
          });
        } else {
          throw new Error('Failed to save image locally');
        }
      } else {
        throw new Error('Image generation failed');
      }
    } catch (error) {
      console.log(`❌ ${style.label} - Error: ${error.message}`);
      results.push({
        style,
        success: false,
        error: error.message
      });
    }

    // Add delay between requests (except for the last one)
    if (i < ImageStyles.length - 1) {
      console.log(`⏳ Waiting ${DELAY_BETWEEN_REQUESTS}ms before next request...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_REQUESTS));
    }
  }

  // Generate summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const skipped = results.filter(r => r.skipped).length;
  const generated = results.filter(r => r.success && !r.skipped).length;

  console.log('\n' + '='.repeat(50));
  console.log('📊 GENERATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Total successful: ${successful}`);
  console.log(`🆕 Newly generated: ${generated}`);
  console.log(`⏭️  Skipped (existing): ${skipped}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📁 Output directory: ${OUTPUT_DIR}`);

  if (failed > 0) {
    console.log('\n❌ Failed styles:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.style.label}: ${r.error}`);
    });
  }

  if (generated > 0) {
    console.log('\n🆕 Newly generated:');
    results.filter(r => r.success && !r.skipped).forEach(r => {
      console.log(`  - ${r.style.label}: ${r.filepath}`);
    });
  }

  // Save metadata
  const metadata = {
    generated_at: new Date().toISOString(),
    agent_url: AGENT_URL,
    agent_id: AGENT_ID,
    base_prompt: BASE_PROMPT,
    uses_style_specific_prompts: true,
    total_styles: ImageStyles.length,
    successful: successful,
    newly_generated: generated,
    skipped: skipped,
    failed: failed,
    results: results,
  };

  const metadataPath = path.join(OUTPUT_DIR, 'ai-generation-metadata.json');
  fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

  console.log(`\n📄 Metadata saved to ai-generation-metadata.json`);
  console.log('\n🎉 AI image generation complete!');

  if (generated > 0) {
    console.log('\n💡 Next steps:');
    console.log('1. Check the generated images in the output directory');
    console.log('2. Refresh your frontend to see the new style samples');
    console.log('3. The canvas editor will automatically use these images');
  }
}

// Run the script
if (require.main === module) {
  generateAllStyleSamples().catch(console.error);
}

module.exports = { generateAllStyleSamples };
